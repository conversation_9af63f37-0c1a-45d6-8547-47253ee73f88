<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Core\Router;

// Load environment variables if .env file exists
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

// Start session
session_start();

// Initialize application
$app = new Application();

// Initialize router
$router = new Router();

// Define routes
$router->get('/', 'HomeController@index');
$router->get('/login', 'AuthController@showLogin');
$router->post('/login', 'AuthController@login');
$router->post('/logout', 'AuthController@logout');

// Admin routes (protected)
$router->middleware('AdminMiddleware')->get('/admin', 'AdminController@dashboard');
$router->middleware('AdminMiddleware')->get('/admin/users', 'UserController@index');
$router->middleware('AdminMiddleware')->get('/admin/users/create', 'UserController@create');
$router->middleware('AdminMiddleware')->post('/admin/users', 'UserController@store');
$router->middleware('AdminMiddleware')->get('/admin/users/{id}/edit', 'UserController@edit');
$router->middleware('AdminMiddleware')->put('/admin/users/{id}', 'UserController@update');
$router->middleware('AdminMiddleware')->delete('/admin/users/{id}', 'UserController@delete');

$router->middleware('AdminMiddleware')->get('/admin/groups', 'GroupController@index');
$router->middleware('AdminMiddleware')->get('/admin/groups/create', 'GroupController@create');
$router->middleware('AdminMiddleware')->post('/admin/groups', 'GroupController@store');
$router->middleware('AdminMiddleware')->get('/admin/groups/{id}/edit', 'GroupController@edit');
$router->middleware('AdminMiddleware')->put('/admin/groups/{id}', 'GroupController@update');
$router->middleware('AdminMiddleware')->delete('/admin/groups/{id}', 'GroupController@delete');

// Task routes
$router->middleware('AdminMiddleware')->get('/admin/tasks', 'TaskController@index');
$router->middleware('AdminMiddleware')->get('/admin/tasks/create', 'TaskController@create');
$router->middleware('AdminMiddleware')->post('/admin/tasks', 'TaskController@store');
$router->middleware('AdminMiddleware')->get('/admin/tasks/{id}', 'TaskController@show');
$router->middleware('AdminMiddleware')->get('/admin/tasks/{id}/edit', 'TaskController@edit');
$router->middleware('AdminMiddleware')->put('/admin/tasks/{id}', 'TaskController@update');
$router->middleware('AdminMiddleware')->delete('/admin/tasks/{id}', 'TaskController@delete');

// Task routes
$router->middleware('AdminMiddleware')->get('/admin/tasks', 'TaskController@index');
$router->middleware('AdminMiddleware')->get('/admin/tasks/create', 'TaskController@create');
$router->middleware('AdminMiddleware')->post('/admin/tasks', 'TaskController@store');
$router->middleware('AdminMiddleware')->get('/admin/tasks/{id}', 'TaskController@show');
$router->middleware('AdminMiddleware')->get('/admin/tasks/{id}/edit', 'TaskController@edit');
$router->middleware('AdminMiddleware')->put('/admin/tasks/{id}', 'TaskController@update');
$router->middleware('AdminMiddleware')->delete('/admin/tasks/{id}', 'TaskController@delete');
$router->middleware('AdminMiddleware')->post('/admin/tasks/{id}/comments', 'TaskController@addComment');
$router->middleware('AdminMiddleware')->post('/admin/tasks/{id}/attachments', 'TaskController@addAttachment');
$router->middleware('AdminMiddleware')->delete('/admin/tasks/{id}/attachments/{attachmentId}', 'TaskController@deleteAttachment');
$router->middleware('AdminMiddleware')->get('/admin/tasks/{id}/attachments/{attachmentId}/download', 'TaskController@downloadAttachment');
$router->middleware('AdminMiddleware')->get('/admin/tasks/{id}/attachments/{attachmentId}/preview', 'TaskController@previewAttachment');

// Storage routes
$router->middleware('AdminMiddleware')->get('/admin/storage', 'StorageController@index');
$router->middleware('AdminMiddleware')->post('/admin/storage/upload', 'StorageController@upload');
$router->middleware('AdminMiddleware')->get('/admin/storage/download', 'StorageController@download');
$router->middleware('AdminMiddleware')->post('/admin/storage/create-folder', 'StorageController@createFolder');
$router->middleware('AdminMiddleware')->post('/admin/storage/delete', 'StorageController@delete');
$router->middleware('AdminMiddleware')->post('/admin/storage/rename', 'StorageController@rename');

// User settings routes
$router->middleware('AdminMiddleware')->get('/admin/profile', 'UserSettingsController@profile');
$router->middleware('AdminMiddleware')->post('/admin/profile/update', 'UserSettingsController@updateProfile');
$router->middleware('AdminMiddleware')->get('/admin/profile/nextcloud-settings', 'UserSettingsController@nextcloudSettings');
$router->middleware('AdminMiddleware')->post('/admin/profile/nextcloud-settings/update', 'UserSettingsController@updateNextcloudSettings');
$router->middleware('AdminMiddleware')->post('/admin/profile/nextcloud-settings/test', 'UserSettingsController@testNextcloudConnection');
$router->middleware('AdminMiddleware')->post('/admin/profile/nextcloud-settings/delete', 'UserSettingsController@deleteNextcloudSettings');
$router->middleware('AdminMiddleware')->get('/admin/profile/nextcloud-settings/get', 'UserSettingsController@getNextcloudSettings');

// StrixBudget routes
$router->middleware('AdminMiddleware')->get('/admin/strixbudget', 'StrixBudgetController@dashboard');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/settings', 'StrixBudgetController@settings');
$router->middleware('AdminMiddleware')->post('/admin/strixbudget/settings/update', 'StrixBudgetController@updateSettings');
$router->middleware('AdminMiddleware')->post('/admin/strixbudget/settings/test', 'StrixBudgetController@testConnection');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/settings/debug', 'StrixBudgetController@debugInfo');
$router->middleware('AdminMiddleware')->post('/admin/strixbudget/settings/delete', 'StrixBudgetController@deleteSettings');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/settings/get', 'StrixBudgetController@getSettings');

// StrixBudget Bank Accounts routes
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/bank-accounts', 'StrixBudget\\BankAccountController@index');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/bank-accounts/create', 'StrixBudget\\BankAccountController@create');
$router->middleware('AdminMiddleware')->post('/admin/strixbudget/bank-accounts', 'StrixBudget\\BankAccountController@store');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/bank-accounts/{id}', 'StrixBudget\\BankAccountController@show');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/bank-accounts/{id}/edit', 'StrixBudget\\BankAccountController@edit');
$router->middleware('AdminMiddleware')->put('/admin/strixbudget/bank-accounts/{id}', 'StrixBudget\\BankAccountController@update');
$router->middleware('AdminMiddleware')->delete('/admin/strixbudget/bank-accounts/{id}', 'StrixBudget\\BankAccountController@delete');

// StrixBudget Transactions routes
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transactions', 'StrixBudget\\TransactionController@index');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transactions/create', 'StrixBudget\\TransactionController@create');
$router->middleware('AdminMiddleware')->post('/admin/strixbudget/transactions', 'StrixBudget\\TransactionController@store');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transactions/{id}', 'StrixBudget\\TransactionController@show');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transactions/{id}/edit', 'StrixBudget\\TransactionController@edit');
$router->middleware('AdminMiddleware')->put('/admin/strixbudget/transactions/{id}', 'StrixBudget\\TransactionController@update');
$router->middleware('AdminMiddleware')->delete('/admin/strixbudget/transactions/{id}', 'StrixBudget\\TransactionController@delete');

// StrixBudget Transfers routes
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transfers', 'StrixBudget\\TransferController@index');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transfers/create', 'StrixBudget\\TransferController@create');
$router->middleware('AdminMiddleware')->post('/admin/strixbudget/transfers', 'StrixBudget\\TransferController@store');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transfers/{id}', 'StrixBudget\\TransferController@show');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transfers/{id}/edit', 'StrixBudget\\TransferController@edit');
$router->middleware('AdminMiddleware')->put('/admin/strixbudget/transfers/{id}', 'StrixBudget\\TransferController@update');
$router->middleware('AdminMiddleware')->delete('/admin/strixbudget/transfers/{id}', 'StrixBudget\\TransferController@delete');

// StrixBudget Counterparties routes
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/counterparties', 'StrixBudget\\CounterpartyController@index');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/counterparties/create', 'StrixBudget\\CounterpartyController@create');
$router->middleware('AdminMiddleware')->post('/admin/strixbudget/counterparties', 'StrixBudget\\CounterpartyController@store');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/counterparties/{id}', 'StrixBudget\\CounterpartyController@show');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/counterparties/{id}/edit', 'StrixBudget\\CounterpartyController@edit');
$router->middleware('AdminMiddleware')->put('/admin/strixbudget/counterparties/{id}', 'StrixBudget\\CounterpartyController@update');
$router->middleware('AdminMiddleware')->delete('/admin/strixbudget/counterparties/{id}', 'StrixBudget\\CounterpartyController@delete');

// StrixBudget Transaction Types routes
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transaction-types', 'StrixBudget\\TransactionTypeController@index');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transaction-types/create', 'StrixBudget\\TransactionTypeController@create');
$router->middleware('AdminMiddleware')->post('/admin/strixbudget/transaction-types', 'StrixBudget\\TransactionTypeController@store');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transaction-types/{id}', 'StrixBudget\\TransactionTypeController@show');
$router->middleware('AdminMiddleware')->get('/admin/strixbudget/transaction-types/{id}/edit', 'StrixBudget\\TransactionTypeController@edit');
$router->middleware('AdminMiddleware')->put('/admin/strixbudget/transaction-types/{id}', 'StrixBudget\\TransactionTypeController@update');
$router->middleware('AdminMiddleware')->delete('/admin/strixbudget/transaction-types/{id}', 'StrixBudget\\TransactionTypeController@delete');

// Public API routes
$router->get('/api/health', 'Api\\StrixBudgetApiController@health'); // Public health check endpoint
$router->head('/api/health', 'Api\\StrixBudgetApiController@health'); // HEAD request for health check

// StrixBudget API routes for frontend
$router->middleware('AdminMiddleware')->post('/api/strixbudget/auth/login', 'Api\\StrixBudgetApiController@login');
$router->middleware('AdminMiddleware')->get('/api/strixbudget/auth/user', 'Api\\StrixBudgetApiController@getCurrentUser');
$router->middleware('AdminMiddleware')->get('/api/strixbudget/bank-accounts', 'Api\\StrixBudgetApiController@getBankAccounts');
$router->middleware('AdminMiddleware')->get('/api/strixbudget/transactions', 'Api\\StrixBudgetApiController@getTransactions');
$router->middleware('AdminMiddleware')->post('/api/strixbudget/transactions', 'Api\\StrixBudgetApiController@createTransaction');
$router->middleware('AdminMiddleware')->get('/api/strixbudget/counterparties', 'Api\\StrixBudgetApiController@getCounterparties');
$router->middleware('AdminMiddleware')->get('/api/strixbudget/statistics', 'Api\\StrixBudgetApiController@getStatistics');
$router->middleware('AdminMiddleware')->post('/api/strixbudget/test-connection', 'Api\\StrixBudgetApiController@testConnection');

// Handle the request
$router->dispatch($_SERVER['REQUEST_METHOD'], $_SERVER['REQUEST_URI']);
