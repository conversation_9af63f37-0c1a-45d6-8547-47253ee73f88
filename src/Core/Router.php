<?php

namespace Strix\ERP\Core;

class Router
{
    private array $routes = [];
    private array $globalMiddlewares = [];
    private array $routeMiddlewares = [];

    public function get(string $path, string $handler): void
    {
        $this->addRoute('GET', $path, $handler);
    }

    public function post(string $path, string $handler): void
    {
        $this->addRoute('POST', $path, $handler);
    }

    public function put(string $path, string $handler): void
    {
        $this->addRoute('PUT', $path, $handler);
    }

    public function delete(string $path, string $handler): void
    {
        $this->addRoute('DELETE', $path, $handler);
    }

    public function head(string $path, string $handler): void
    {
        $this->addRoute('HEAD', $path, $handler);
    }

    private function addRoute(string $method, string $path, string $handler): void
    {
        $routeIndex = count($this->routes);
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler,
            'middlewares' => []
        ];

        // Add any pending middlewares to this route
        if (!empty($this->routeMiddlewares)) {
            $this->routes[$routeIndex]['middlewares'] = $this->routeMiddlewares;
            $this->routeMiddlewares = []; // Clear for next route
        }
    }

    public function middleware(string $middleware): self
    {
        $this->routeMiddlewares[] = $middleware;
        return $this;
    }

    public function dispatch(string $method, string $uri): void
    {
        // Remove query string from URI
        $uri = parse_url($uri, PHP_URL_PATH);
        
        // Handle PUT and DELETE methods from forms
        if ($method === 'POST' && isset($_POST['_method'])) {
            $method = strtoupper($_POST['_method']);
        }

        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }

            $pattern = $this->convertToRegex($route['path']);
            if (preg_match($pattern, $uri, $matches)) {
                array_shift($matches); // Remove full match
                
                try {
                    // Run global middlewares
                    foreach ($this->globalMiddlewares as $middleware) {
                        $this->runMiddleware($middleware);
                    }

                    // Run route-specific middlewares
                    foreach ($route['middlewares'] as $middleware) {
                        $this->runMiddleware($middleware);
                    }

                    // Run controller action
                    $this->runController($route['handler'], $matches);
                    return;
                } catch (\Throwable $e) {
                    Application::getInstance()->handleError($e);
                    return;
                }
            }
        }

        // No route found
        http_response_code(404);
        $this->render404();
    }

    private function convertToRegex(string $path): string
    {
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $path);
        return '#^' . $pattern . '$#';
    }

    private function runMiddleware(string $middleware): void
    {
        $className = "Strix\\ERP\\Middleware\\{$middleware}";
        if (class_exists($className)) {
            $instance = new $className();
            $instance->handle();
        }
    }

    private function runController(string $handler, array $params): void
    {
        [$controllerName, $action] = explode('@', $handler);
        $controllerClass = "Strix\\ERP\\Controllers\\{$controllerName}";
        
        if (!class_exists($controllerClass)) {
            throw new \RuntimeException("Controller {$controllerClass} not found");
        }
        
        $controller = new $controllerClass();
        
        if (!method_exists($controller, $action)) {
            throw new \RuntimeException("Action {$action} not found in {$controllerClass}");
        }
        
        call_user_func_array([$controller, $action], $params);
    }

    private function render404(): void
    {
        echo '<!DOCTYPE html>
<html>
<head>
    <title>404 - Страницата не е намерена</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        h1 { color: #e74c3c; }
    </style>
</head>
<body>
    <h1>404</h1>
    <p>Страницата, която търсите, не е намерена.</p>
    <a href="/">Върнете се към началната страница</a>
</body>
</html>';
    }
}
