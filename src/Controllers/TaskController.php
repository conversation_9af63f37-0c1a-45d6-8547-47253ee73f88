<?php

namespace Strix\ERP\Controllers;

use Strix\ERP\Core\Controller;
use Strix\ERP\Models\Task;
use Strix\ERP\Models\TaskType;
use Strix\ERP\Models\TaskStatus;
use Strix\ERP\Models\TaskComment;
use Strix\ERP\Models\TaskAttachment;
use Strix\ERP\Models\User;
use Strix\ERP\Models\Group;

class TaskController extends Controller
{
    public function index(): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.view');
        
        $search = $this->getInput('search', '');
        $statusFilter = $this->getInput('status', '');
        $typeFilter = $this->getInput('type', '');
        $priorityFilter = $this->getInput('priority', '');
        $assigneeFilter = $this->getInput('assignee', '');
        $page = max(1, (int) $this->getInput('page', 1));
        $perPage = 20;
        
        $currentUser = $this->app->getCurrentUser();
        $canViewAll = $this->app->hasPermission('tasks.view_all');
        
        // Build query
        $sql = "SELECT DISTINCT t.*, 
                       tt.name as type_name, tt.color as type_color,
                       ts.name as status_name, ts.color as status_color,
                       u1.first_name as creator_first_name, u1.last_name as creator_last_name,
                       u2.first_name as assignee_first_name, u2.last_name as assignee_last_name
                FROM tasks t
                INNER JOIN task_types tt ON t.task_type_id = tt.id
                INNER JOIN task_statuses ts ON t.status_id = ts.id
                INNER JOIN users u1 ON t.created_by = u1.id
                LEFT JOIN users u2 ON t.assigned_to = u2.id
                LEFT JOIN task_assignees ta ON t.id = ta.task_id
                LEFT JOIN user_groups ug ON ta.group_id = ug.group_id
                WHERE 1=1";
        
        $params = [];
        
        // Filter by user access
        if (!$canViewAll) {
            $sql .= " AND (t.created_by = ? OR t.assigned_to = ? OR ta.user_id = ? OR ug.user_id = ?)";
            $params = array_merge($params, [$currentUser['id'], $currentUser['id'], $currentUser['id'], $currentUser['id']]);
        }
        
        // Apply filters
        if ($search) {
            $sql .= " AND (t.title LIKE ? OR t.description LIKE ?)";
            $params = array_merge($params, ["%$search%", "%$search%"]);
        }
        
        if ($statusFilter) {
            $sql .= " AND t.status_id = ?";
            $params[] = $statusFilter;
        }
        
        if ($typeFilter) {
            $sql .= " AND t.task_type_id = ?";
            $params[] = $typeFilter;
        }
        
        if ($priorityFilter) {
            $sql .= " AND t.priority = ?";
            $params[] = $priorityFilter;
        }
        
        if ($assigneeFilter) {
            $sql .= " AND (t.assigned_to = ? OR ta.user_id = ?)";
            $params = array_merge($params, [$assigneeFilter, $assigneeFilter]);
        }
        
        $sql .= " ORDER BY t.created_at DESC";
        
        // Get total count - build a simpler count query
        $countSql = "SELECT COUNT(DISTINCT t.id)
                     FROM tasks t
                     INNER JOIN task_types tt ON t.task_type_id = tt.id
                     INNER JOIN task_statuses ts ON t.status_id = ts.id
                     INNER JOIN users u1 ON t.created_by = u1.id
                     LEFT JOIN users u2 ON t.assigned_to = u2.id
                     LEFT JOIN task_assignees ta ON t.id = ta.task_id
                     LEFT JOIN user_groups ug ON ta.group_id = ug.group_id
                     WHERE 1=1";

        // Apply the same filters to count query
        if (!$canViewAll) {
            $countSql .= " AND (t.created_by = ? OR t.assigned_to = ? OR ta.user_id = ? OR ug.user_id = ?)";
        }

        if ($search) {
            $countSql .= " AND (t.title LIKE ? OR t.description LIKE ?)";
        }

        if ($statusFilter) {
            $countSql .= " AND t.status_id = ?";
        }

        if ($typeFilter) {
            $countSql .= " AND t.task_type_id = ?";
        }

        if ($priorityFilter) {
            $countSql .= " AND t.priority = ?";
        }

        if ($assigneeFilter) {
            $countSql .= " AND (t.assigned_to = ? OR ta.user_id = ?)";
        }

        $totalTasks = (int) \Strix\ERP\Core\Database::fetchColumn($countSql, $params);
        
        // Add pagination
        $sql .= " LIMIT " . (($page - 1) * $perPage) . ", $perPage";
        
        $results = \Strix\ERP\Core\Database::fetchAll($sql, $params);
        
        $tasks = [];
        foreach ($results as $data) {
            $task = new Task();
            foreach ($data as $key => $value) {
                $task->$key = $value;
            }

            // Pre-calculate computed properties to avoid repeated calculations in view
            $task->priority_color = $this->getPriorityColor($task->priority);
            $task->priority_label = $this->getPriorityLabel($task->priority);

            if ($task->due_date) {
                $task->days_until_due = $this->getDaysUntilDue($task->due_date);
                $task->is_overdue = $this->isOverdue($task->due_date, $task->status_name);
            }

            $tasks[] = $task;
        }
        
        $totalPages = ceil($totalTasks / $perPage);
        
        // Get filter options
        $taskTypes = TaskType::getActiveTypes();
        $taskStatuses = TaskStatus::getActiveStatuses();
        $users = User::getActiveUsers();
        
        $this->view('admin/tasks/index', [
            'title' => 'Управление на задачи',
            'showSidebar' => true,
            'tasks' => $tasks,
            'taskTypes' => $taskTypes,
            'taskStatuses' => $taskStatuses,
            'users' => $users,
            'search' => $search,
            'statusFilter' => $statusFilter,
            'typeFilter' => $typeFilter,
            'priorityFilter' => $priorityFilter,
            'assigneeFilter' => $assigneeFilter,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalTasks' => $totalTasks
        ]);
    }
    
    public function create(): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.create');
        
        $taskTypes = TaskType::getActiveTypes();
        $taskStatuses = TaskStatus::getActiveStatuses();
        $users = User::getActiveUsers();
        $groups = Group::getActiveGroups();
        
        $this->view('admin/tasks/create', [
            'title' => 'Създаване на задача',
            'showSidebar' => true,
            'taskTypes' => $taskTypes,
            'taskStatuses' => $taskStatuses,
            'users' => $users,
            'groups' => $groups,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    public function store(): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.create');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/tasks/create', 'error', 'Невалидна заявка');
        }
        
        $data = $this->getAllInput();
        
        // Validate input
        $errors = $this->validate([
            'title' => 'required|max:255',
            'task_type_id' => 'required',
            'status_id' => 'required',
            'priority' => 'required'
        ]);
        
        if (!empty($errors)) {
            $taskTypes = TaskType::getActiveTypes();
            $taskStatuses = TaskStatus::getActiveStatuses();
            $users = User::getActiveUsers();
            $groups = Group::getActiveGroups();
            
            $this->view('admin/tasks/create', [
                'title' => 'Създаване на задача',
                'showSidebar' => true,
                'taskTypes' => $taskTypes,
                'taskStatuses' => $taskStatuses,
                'users' => $users,
                'groups' => $groups,
                'errors' => $errors,
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }
        
        try {
            $currentUser = $this->app->getCurrentUser();
            
            // Create task
            $task = new Task();
            $task->title = $data['title'];
            $task->description = $data['description'] ?? '';
            $task->task_type_id = (int) $data['task_type_id'];
            $task->status_id = (int) $data['status_id'];
            $task->priority = $data['priority'];
            $task->progress = (int) ($data['progress'] ?? 0);
            $task->due_date = !empty($data['due_date']) ? $data['due_date'] : null;
            $task->start_date = !empty($data['start_date']) ? $data['start_date'] : null;
            $task->estimated_hours = !empty($data['estimated_hours']) ? (float) $data['estimated_hours'] : null;
            $task->created_by = $currentUser['id'];
            $task->assigned_to = !empty($data['assigned_to']) ? (int) $data['assigned_to'] : null;
            
            if ($task->save()) {
                // Assign additional users
                if (!empty($data['assignee_users']) && is_array($data['assignee_users'])) {
                    foreach ($data['assignee_users'] as $userId) {
                        $task->assignUser((int) $userId, $currentUser['id']);
                    }
                }
                
                // Assign groups
                if (!empty($data['assignee_groups']) && is_array($data['assignee_groups'])) {
                    foreach ($data['assignee_groups'] as $groupId) {
                        $task->assignGroup((int) $groupId, $currentUser['id']);
                    }
                }
                
                $this->redirectWithMessage('/admin/tasks', 'success', 'Задачата е създадена успешно');
            } else {
                throw new \Exception('Грешка при запазване на задачата');
            }
        } catch (\Exception $e) {
            $taskTypes = TaskType::getActiveTypes();
            $taskStatuses = TaskStatus::getActiveStatuses();
            $users = User::getActiveUsers();
            $groups = Group::getActiveGroups();
            
            $this->view('admin/tasks/create', [
                'title' => 'Създаване на задача',
                'showSidebar' => true,
                'taskTypes' => $taskTypes,
                'taskStatuses' => $taskStatuses,
                'users' => $users,
                'groups' => $groups,
                'error' => 'Грешка при създаване на задачата: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    public function show(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.view');
        
        $task = Task::find($id);
        if (!$task) {
            $this->redirectWithMessage('/admin/tasks', 'error', 'Задачата не е намерена');
        }
        
        // Check access permissions
        $currentUser = $this->app->getCurrentUser();
        $canViewAll = $this->app->hasPermission('tasks.view_all');
        
        if (!$canViewAll) {
            $assignees = $task->getAssignees();
            $hasAccess = false;
            
            // Check if user is creator, primary assignee, or in assignee list
            if ($task->created_by === $currentUser['id'] || $task->assigned_to === $currentUser['id']) {
                $hasAccess = true;
            } else {
                foreach ($assignees as $assignee) {
                    if ($assignee['type'] === 'user' && $assignee['id'] === $currentUser['id']) {
                        $hasAccess = true;
                        break;
                    } elseif ($assignee['type'] === 'group') {
                        // Check if user is in this group
                        $userGroups = User::find($currentUser['id'])->getGroups();
                        foreach ($userGroups as $userGroup) {
                            if ($userGroup->id === $assignee['id']) {
                                $hasAccess = true;
                                break 2;
                            }
                        }
                    }
                }
            }
            
            if (!$hasAccess) {
                $this->redirectWithMessage('/admin/tasks', 'error', 'Нямате права за преглед на тази задача');
            }
        }
        
        $comments = $task->getComments();
        $attachments = $task->getAttachments();
        $assignees = $task->getAssignees();
        
        $this->view('admin/tasks/show', [
            'title' => 'Преглед на задача: ' . $task->title,
            'showSidebar' => true,
            'task' => $task,
            'comments' => $comments,
            'attachments' => $attachments,
            'assignees' => $assignees,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    public function edit(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.edit');

        $task = Task::find($id);
        if (!$task) {
            $this->redirectWithMessage('/admin/tasks', 'error', 'Задачата не е намерена');
        }

        // Check edit permissions
        $currentUser = $this->app->getCurrentUser();
        $canEditAll = $this->app->hasPermission('tasks.edit_all');

        if (!$canEditAll && $task->created_by !== $currentUser['id']) {
            $this->redirectWithMessage('/admin/tasks', 'error', 'Нямате права за редактиране на тази задача');
        }

        $taskTypes = TaskType::getActiveTypes();
        $taskStatuses = TaskStatus::getActiveStatuses();
        $users = User::getActiveUsers();
        $groups = Group::getActiveGroups();
        $assignees = $task->getAssignees();

        $this->view('admin/tasks/edit', [
            'title' => 'Редактиране на задача',
            'showSidebar' => true,
            'task' => $task,
            'taskTypes' => $taskTypes,
            'taskStatuses' => $taskStatuses,
            'users' => $users,
            'groups' => $groups,
            'assignees' => $assignees,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    public function update(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.edit');

        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/tasks', 'error', 'Невалидна заявка');
        }

        $task = Task::find($id);
        if (!$task) {
            $this->redirectWithMessage('/admin/tasks', 'error', 'Задачата не е намерена');
        }

        // Check edit permissions
        $currentUser = $this->app->getCurrentUser();
        $canEditAll = $this->app->hasPermission('tasks.edit_all');

        if (!$canEditAll && $task->created_by !== $currentUser['id']) {
            $this->redirectWithMessage('/admin/tasks', 'error', 'Нямате права за редактиране на тази задача');
        }

        $data = $this->getAllInput();

        // Validate input
        $errors = $this->validate([
            'title' => 'required|max:255',
            'task_type_id' => 'required',
            'status_id' => 'required',
            'priority' => 'required'
        ]);

        if (!empty($errors)) {
            $taskTypes = TaskType::getActiveTypes();
            $taskStatuses = TaskStatus::getActiveStatuses();
            $users = User::getActiveUsers();
            $groups = Group::getActiveGroups();
            $assignees = $task->getAssignees();

            $this->view('admin/tasks/edit', [
                'title' => 'Редактиране на задача',
                'showSidebar' => true,
                'task' => $task,
                'taskTypes' => $taskTypes,
                'taskStatuses' => $taskStatuses,
                'users' => $users,
                'groups' => $groups,
                'assignees' => $assignees,
                'errors' => $errors,
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }

        try {
            // Update task
            $task->title = $data['title'];
            $task->description = $data['description'] ?? '';
            $task->task_type_id = (int) $data['task_type_id'];
            $task->status_id = (int) $data['status_id'];
            $task->priority = $data['priority'];
            $task->progress = (int) ($data['progress'] ?? 0);
            $task->due_date = !empty($data['due_date']) ? $data['due_date'] : null;
            $task->start_date = !empty($data['start_date']) ? $data['start_date'] : null;
            $task->estimated_hours = !empty($data['estimated_hours']) ? (float) $data['estimated_hours'] : null;
            $task->assigned_to = !empty($data['assigned_to']) ? (int) $data['assigned_to'] : null;

            if ($task->save()) {
                $this->redirectWithMessage('/admin/tasks/' . $task->id, 'success', 'Задачата е обновена успешно');
            } else {
                throw new \Exception('Грешка при запазване на задачата');
            }
        } catch (\Exception $e) {
            $taskTypes = TaskType::getActiveTypes();
            $taskStatuses = TaskStatus::getActiveStatuses();
            $users = User::getActiveUsers();
            $groups = Group::getActiveGroups();
            $assignees = $task->getAssignees();

            $this->view('admin/tasks/edit', [
                'title' => 'Редактиране на задача',
                'showSidebar' => true,
                'task' => $task,
                'taskTypes' => $taskTypes,
                'taskStatuses' => $taskStatuses,
                'users' => $users,
                'groups' => $groups,
                'assignees' => $assignees,
                'error' => 'Грешка при обновяване на задачата: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }

    public function delete(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.delete');

        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }

        $task = Task::find($id);
        if (!$task) {
            $this->json(['error' => 'Задачата не е намерена'], 404);
            return;
        }

        // Check delete permissions
        $currentUser = $this->app->getCurrentUser();
        $canEditAll = $this->app->hasPermission('tasks.edit_all');

        if (!$canEditAll && $task->created_by !== $currentUser['id']) {
            $this->json(['error' => 'Нямате права за изтриване на тази задача'], 403);
            return;
        }

        try {
            if ($task->delete()) {
                $this->json(['success' => true, 'message' => 'Задачата е изтрита успешно']);
            } else {
                $this->json(['error' => 'Грешка при изтриване на задачата'], 500);
            }
        } catch (\Exception $e) {
            $this->json(['error' => 'Грешка при изтриване: ' . $e->getMessage()], 500);
        }
    }

    public function addComment(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.comment');

        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Невалидна заявка');
            return;
        }

        $task = Task::find($id);
        if (!$task) {
            $this->redirectWithMessage('/admin/tasks', 'error', 'Задачата не е намерена');
            return;
        }

        // Check access permissions
        $currentUser = $this->app->getCurrentUser();
        $canViewAll = $this->app->hasPermission('tasks.view_all');

        if (!$canViewAll) {
            $assignees = $task->getAssignees();
            $hasAccess = false;

            // Check if user is creator, primary assignee, or in assignee list
            if ($task->created_by === $currentUser['id'] || $task->assigned_to === $currentUser['id']) {
                $hasAccess = true;
            } else {
                foreach ($assignees as $assignee) {
                    if ($assignee['type'] === 'user' && $assignee['id'] === $currentUser['id']) {
                        $hasAccess = true;
                        break;
                    } elseif ($assignee['type'] === 'group') {
                        // Check if user is in this group
                        $userGroups = User::find($currentUser['id'])->getGroups();
                        foreach ($userGroups as $userGroup) {
                            if ($userGroup->id === $assignee['id']) {
                                $hasAccess = true;
                                break 2;
                            }
                        }
                    }
                }
            }

            if (!$hasAccess) {
                $this->redirectWithMessage('/admin/tasks', 'error', 'Нямате права за коментиране на тази задача');
                return;
            }
        }

        $content = trim($this->getInput('content', ''));
        if (empty($content)) {
            $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Коментарът не може да бъде празен');
            return;
        }

        try {
            $comment = $task->addComment($currentUser['id'], $content);
            if ($comment) {
                $this->redirectWithMessage('/admin/tasks/' . $id, 'success', 'Коментарът е добавен успешно');
            } else {
                $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Грешка при добавяне на коментара');
            }
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Грешка при добавяне на коментара: ' . $e->getMessage());
        }
    }

    public function addAttachment(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.attach');

        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Невалидна заявка');
            return;
        }

        $task = Task::find($id);
        if (!$task) {
            $this->redirectWithMessage('/admin/tasks', 'error', 'Задачата не е намерена');
            return;
        }

        // Check access permissions
        $currentUser = $this->app->getCurrentUser();
        $canViewAll = $this->app->hasPermission('tasks.view_all');

        if (!$canViewAll) {
            $assignees = $task->getAssignees();
            $hasAccess = false;

            // Check if user is creator, primary assignee, or in assignee list
            if ($task->created_by === $currentUser['id'] || $task->assigned_to === $currentUser['id']) {
                $hasAccess = true;
            } else {
                foreach ($assignees as $assignee) {
                    if ($assignee['type'] === 'user' && $assignee['id'] === $currentUser['id']) {
                        $hasAccess = true;
                        break;
                    } elseif ($assignee['type'] === 'group') {
                        // Check if user is in this group
                        $userGroups = User::find($currentUser['id'])->getGroups();
                        foreach ($userGroups as $userGroup) {
                            if ($userGroup->id === $assignee['id']) {
                                $hasAccess = true;
                                break 2;
                            }
                        }
                    }
                }
            }

            if (!$hasAccess) {
                $this->redirectWithMessage('/admin/tasks', 'error', 'Нямате права за прикачване на файлове към тази задача');
                return;
            }
        }

        if (!isset($_FILES['attachment']) || $_FILES['attachment']['error'] !== UPLOAD_ERR_OK) {
            $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Моля изберете файл за прикачване');
            return;
        }

        $file = $_FILES['attachment'];

        // Validate file size (max 10MB)
        $maxSize = 10 * 1024 * 1024; // 10MB
        if ($file['size'] > $maxSize) {
            $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Файлът е твърде голям. Максимален размер: 10MB');
            return;
        }

        // Validate file type
        $allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain', 'text/csv',
            'application/zip', 'application/x-rar-compressed'
        ];

        if (!in_array($file['type'], $allowedTypes)) {
            $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Неподдържан тип файл');
            return;
        }

        try {
            // Create uploads directory if it doesn't exist
            $uploadDir = __DIR__ . '/../../uploads/tasks/' . $id;
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $storedName = uniqid() . '.' . $extension;
            $filePath = $uploadDir . '/' . $storedName;

            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filePath)) {
                // Save to database
                $attachment = $task->addAttachment(
                    $currentUser['id'],
                    $file['name'],
                    $storedName,
                    'uploads/tasks/' . $id . '/' . $storedName,
                    $file['size'],
                    $file['type']
                );

                if ($attachment) {
                    $this->redirectWithMessage('/admin/tasks/' . $id, 'success', 'Файлът е прикачен успешно');
                } else {
                    // Clean up file if database save failed
                    unlink($filePath);
                    $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Грешка при запазване на файла в базата данни');
                }
            } else {
                $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Грешка при качване на файла');
            }
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Грешка при прикачване на файла: ' . $e->getMessage());
        }
    }

    public function deleteAttachment(int $id, int $attachmentId): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.attach');

        if (!$this->validateCsrfToken()) {
            // Check if it's an AJAX request
            if ($this->isAjaxRequest()) {
                $this->json(['error' => 'Невалидна заявка'], 400);
            } else {
                $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Невалидна заявка');
            }
            return;
        }

        $task = Task::find($id);
        if (!$task) {
            if ($this->isAjaxRequest()) {
                $this->json(['error' => 'Задачата не е намерена'], 404);
            } else {
                $this->redirectWithMessage('/admin/tasks', 'error', 'Задачата не е намерена');
            }
            return;
        }

        $attachment = TaskAttachment::find($attachmentId);
        if (!$attachment || $attachment->task_id !== $id) {
            if ($this->isAjaxRequest()) {
                $this->json(['error' => 'Прикачният файл не е намерен'], 404);
            } else {
                $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Прикачният файл не е намерен');
            }
            return;
        }

        // Check permissions - only creator or admin can delete
        $currentUser = $this->app->getCurrentUser();
        $canEditAll = $this->app->hasPermission('tasks.edit_all');

        if (!$canEditAll && $attachment->user_id !== $currentUser['id']) {
            if ($this->isAjaxRequest()) {
                $this->json(['error' => 'Нямате права за изтриване на този файл'], 403);
            } else {
                $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Нямате права за изтриване на този файл');
            }
            return;
        }

        try {
            // Delete physical file
            $filePath = __DIR__ . '/../../' . $attachment->file_path;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Delete from database
            if ($attachment->delete()) {
                if ($this->isAjaxRequest()) {
                    $this->json(['success' => true, 'message' => 'Файлът е изтрит успешно']);
                } else {
                    $this->redirectWithMessage('/admin/tasks/' . $id, 'success', 'Файлът е изтрит успешно');
                }
            } else {
                if ($this->isAjaxRequest()) {
                    $this->json(['error' => 'Грешка при изтриване на файла'], 500);
                } else {
                    $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Грешка при изтриване на файла');
                }
            }
        } catch (\Exception $e) {
            if ($this->isAjaxRequest()) {
                $this->json(['error' => 'Грешка при изтриване: ' . $e->getMessage()], 500);
            } else {
                $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Грешка при изтриване: ' . $e->getMessage());
            }
        }
    }

    public function downloadAttachment(int $id, int $attachmentId): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.view');

        $task = Task::find($id);
        if (!$task) {
            $this->redirectWithMessage('/admin/tasks', 'error', 'Задачата не е намерена');
            return;
        }

        $attachment = TaskAttachment::find($attachmentId);
        if (!$attachment || $attachment->task_id != $id) {
            $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Файлът не е намерен');
            return;
        }

        try {
            // Build full file path
            $filePath = $attachment->file_path;
            if (!str_starts_with($filePath, '/')) {
                // If relative path, make it absolute
                $filePath = __DIR__ . '/../../' . $filePath;
            }

            if (!file_exists($filePath)) {
                $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Файлът не съществува на диска');
                return;
            }

            // Set headers for file download
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $attachment->original_name . '"');
            header('Content-Length: ' . filesize($filePath));
            header('Cache-Control: no-cache, must-revalidate');
            header('Expires: 0');

            // Output file content
            readfile($filePath);
            exit;

        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/tasks/' . $id, 'error', 'Грешка при изтегляне: ' . $e->getMessage());
        }
    }

    public function previewAttachment(int $id, int $attachmentId): void
    {
        $this->requireAuth();
        $this->requirePermission('tasks.view');

        $task = Task::find($id);
        if (!$task) {
            http_response_code(404);
            echo 'Задачата не е намерена';
            return;
        }

        $attachment = TaskAttachment::find($attachmentId);
        if (!$attachment || $attachment->task_id != $id) {
            http_response_code(404);
            echo 'Файлът не е намерен';
            return;
        }

        try {
            // Build full file path
            $filePath = $attachment->file_path;
            if (!str_starts_with($filePath, '/')) {
                // If relative path, make it absolute
                $filePath = __DIR__ . '/../../' . $filePath;
            }

            if (!file_exists($filePath)) {
                http_response_code(404);
                echo 'Файлът не съществува на диска';
                return;
            }

            $extension = strtolower(pathinfo($attachment->original_name, PATHINFO_EXTENSION));
            $textExtensions = ['txt', 'md', 'csv', 'json', 'xml', 'html', 'css', 'js'];

            if (in_array($extension, $textExtensions)) {
                // For text files, return content as plain text
                header('Content-Type: text/plain; charset=utf-8');

                // Limit file size for preview (max 1MB)
                if (filesize($filePath) > 1024 * 1024) {
                    echo 'Файлът е твърде голям за преглед (над 1MB)';
                    return;
                }

                echo file_get_contents($filePath);
            } else {
                http_response_code(400);
                echo 'Този тип файл не може да се прегледа';
            }

        } catch (\Exception $e) {
            http_response_code(500);
            echo 'Грешка при зареждане: ' . $e->getMessage();
        }
    }

    protected function isAjaxRequest(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Helper methods for optimization - avoid repeated calculations in views
     */
    private function getPriorityColor(string $priority): string
    {
        switch ($priority) {
            case 'urgent':
                return '#e74c3c';
            case 'high':
                return '#f39c12';
            case 'normal':
                return '#3498db';
            case 'low':
                return '#95a5a6';
            default:
                return '#95a5a6';
        }
    }

    private function getPriorityLabel(string $priority): string
    {
        switch ($priority) {
            case 'urgent':
                return 'Спешно';
            case 'high':
                return 'Висок';
            case 'normal':
                return 'Нормален';
            case 'low':
                return 'Нисък';
            default:
                return 'Неизвестен';
        }
    }

    private function getDaysUntilDue(string $dueDate): int
    {
        $dueTimestamp = strtotime($dueDate);
        $nowTimestamp = strtotime(date('Y-m-d'));

        return (int) (($dueTimestamp - $nowTimestamp) / (24 * 60 * 60));
    }

    private function isOverdue(string $dueDate, string $statusName): bool
    {
        // Completed tasks are not overdue
        if (in_array($statusName, ['Завършена', 'Отказана'])) {
            return false;
        }

        return strtotime($dueDate) < strtotime(date('Y-m-d'));
    }
}
