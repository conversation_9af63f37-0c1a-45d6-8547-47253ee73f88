<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">👥</div>
        <div class="stat-number"><?= $stats['total_users'] ?></div>
        <div class="stat-label">Общо потребители</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-number"><?= $stats['active_users'] ?></div>
        <div class="stat-label">Активни потребители</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">🏷️</div>
        <div class="stat-number"><?= $stats['total_groups'] ?></div>
        <div class="stat-label">Общо групи</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">🔥</div>
        <div class="stat-number"><?= $stats['active_groups'] ?></div>
        <div class="stat-label">Активни групи</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">🔐</div>
        <div class="stat-number"><?= $stats['total_permissions'] ?></div>
        <div class="stat-label">Общо права</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-number"><?= $stats['total_modules'] ?></div>
        <div class="stat-label">Модули</div>
    </div>

    <?php if (isset($stats['total_tasks'])): ?>
    <div class="stat-card">
        <div class="stat-icon">📋</div>
        <div class="stat-number"><?= $stats['total_tasks'] ?></div>
        <div class="stat-label">Общо задачи</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">⏳</div>
        <div class="stat-number"><?= $stats['in_progress_tasks'] ?? 0 ?></div>
        <div class="stat-label">В прогрес</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-number"><?= $stats['completed_tasks'] ?? 0 ?></div>
        <div class="stat-label">Завършени</div>
    </div>

    <div class="stat-card">
        <div class="stat-icon">⚠️</div>
        <div class="stat-number"><?= $stats['overdue_tasks'] ?></div>
        <div class="stat-label">Просрочени</div>
    </div>
    <?php endif; ?>
</div>

<div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px;">
    <div class="card">
        <div class="card-header">
            <h3>Бързи действия</h3>
        </div>
        <div class="card-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <?php if (isset($user) && $user['permissions'] && in_array('users.create', $user['permissions'])): ?>
                <a href="/admin/users/create" class="btn btn-primary">
                    👤 Създай потребител
                </a>
                <?php endif; ?>
                
                <?php if (isset($user) && $user['permissions'] && in_array('groups.create', $user['permissions'])): ?>
                <a href="/admin/groups/create" class="btn btn-success">
                    🏷️ Създай група
                </a>
                <?php endif; ?>
                
                <?php if (isset($user) && $user['permissions'] && in_array('users.view', $user['permissions'])): ?>
                <a href="/admin/users" class="btn btn-warning">
                    📋 Управление на потребители
                </a>
                <?php endif; ?>
                
                <?php if (isset($user) && $user['permissions'] && in_array('groups.view', $user['permissions'])): ?>
                <a href="/admin/groups" class="btn btn-warning">
                    📋 Управление на групи
                </a>
                <?php endif; ?>

                <?php if ($app->hasPermission('tasks.create')): ?>
                <a href="/admin/tasks/create" class="btn btn-success">
                    📋 Създай задача
                </a>
                <?php endif; ?>

                <?php if ($app->hasPermission('tasks.view')): ?>
                <a href="/admin/tasks" class="btn btn-primary">
                    📋 Всички задачи
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h3>Последни дейности</h3>
        </div>
        <div class="card-body">
            <?php if (!empty($recent_activities)): ?>
                <div style="max-height: 300px; overflow-y: auto;">
                    <?php foreach ($recent_activities as $activity): ?>
                        <div style="padding: 10px 0; border-bottom: 1px solid #eee;">
                            <div style="font-weight: 600; color: #2c3e50;">
                                <?= htmlspecialchars($activity['user']) ?>
                            </div>
                            <div style="color: #7f8c8d; font-size: 14px;">
                                <?= htmlspecialchars($activity['action']) ?>
                                <?php if ($activity['target']): ?>
                                    <strong><?= htmlspecialchars($activity['target']) ?></strong>
                                <?php endif; ?>
                            </div>
                            <div style="color: #95a5a6; font-size: 12px;">
                                <?= htmlspecialchars($activity['time']) ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p style="color: #7f8c8d; text-align: center; padding: 20px;">
                    Няма записани дейности
                </p>
            <?php endif; ?>
        </div>
    </div>
</div>

<div class="card" style="margin-top: 20px;">
    <div class="card-header">
        <h3>Системна информация</h3>
    </div>
    <div class="card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            <div>
                <h4 style="color: #2c3e50; margin-bottom: 10px;">Версия на системата</h4>
                <p>Strix ERP v1.0.0</p>
            </div>
            
            <div>
                <h4 style="color: #2c3e50; margin-bottom: 10px;">PHP версия</h4>
                <p><?= PHP_VERSION ?></p>
            </div>
            
            <div>
                <h4 style="color: #2c3e50; margin-bottom: 10px;">Сървърно време</h4>
                <p><?= date('d.m.Y H:i:s') ?></p>
            </div>
            
            <div>
                <h4 style="color: #2c3e50; margin-bottom: 10px;">Използвана памет</h4>
                <p><?= round(memory_get_usage() / 1024 / 1024, 2) ?> MB</p>
            </div>

            <!-- Nextcloud Status -->
            <div>
                <h4 style="color: #2c3e50; margin-bottom: 10px;">☁️ Nextcloud</h4>
                <?php if ($server_status['nextcloud']['online']): ?>
                    <p style="color: #27ae60; margin-bottom: 5px;">
                        <strong>🟢 Онлайн</strong>
                    </p>
                    <?php if ($server_status['nextcloud']['authenticated']): ?>
                        <p style="color: #27ae60; font-size: 0.9em;">✅ Автентикиран</p>
                    <?php else: ?>
                        <p style="color: #f39c12; font-size: 0.9em;">⚠️ Не е автентикиран</p>
                    <?php endif; ?>
                    <?php if ($server_status['nextcloud']['server_url']): ?>
                        <p style="font-size: 0.8em; color: #7f8c8d;">
                            <?= htmlspecialchars(parse_url($server_status['nextcloud']['server_url'], PHP_URL_HOST)) ?>
                        </p>
                    <?php endif; ?>
                <?php else: ?>
                    <p style="color: #e74c3c; margin-bottom: 5px;">
                        <strong>🔴 Офлайн</strong>
                    </p>
                    <p style="font-size: 0.9em; color: #e74c3c;">
                        <?= htmlspecialchars($server_status['nextcloud']['message']) ?>
                    </p>
                <?php endif; ?>
            </div>

            <!-- StrixBudget Status -->
            <div>
                <h4 style="color: #2c3e50; margin-bottom: 10px;">💰 StrixBudget</h4>
                <?php if ($server_status['strixbudget']['online']): ?>
                    <p style="color: #27ae60; margin-bottom: 5px;">
                        <strong>🟢 Онлайн</strong>
                    </p>
                    <?php if ($server_status['strixbudget']['authenticated']): ?>
                        <p style="color: #27ae60; font-size: 0.9em;">✅ Автентикиран</p>
                    <?php else: ?>
                        <p style="color: #f39c12; font-size: 0.9em;">⚠️ Не е автентикиран</p>
                    <?php endif; ?>
                    <?php if ($server_status['strixbudget']['api_url']): ?>
                        <p style="font-size: 0.8em; color: #7f8c8d;">
                            <?= htmlspecialchars(parse_url($server_status['strixbudget']['api_url'], PHP_URL_HOST)) ?>
                        </p>
                    <?php endif; ?>
                <?php else: ?>
                    <p style="color: #e74c3c; margin-bottom: 5px;">
                        <strong>🔴 Офлайн</strong>
                    </p>
                    <p style="font-size: 0.9em; color: #e74c3c;">
                        <?= htmlspecialchars($server_status['strixbudget']['message']) ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>
