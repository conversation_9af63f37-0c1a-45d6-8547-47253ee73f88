<?php

namespace Strix\ERP\Services;

use Strix\ERP\Models\UserNextcloudSettings;
use Strix\ERP\Models\UserStrixBudgetSettings;

class ServerStatusChecker
{
    /**
     * Check Nextcloud server status
     */
    public static function checkNextcloudStatus(int $userId): array
    {
        try {
            // Get user settings first
            $userSettings = UserNextcloudSettings::getActiveForUser($userId);
            
            if ($userSettings && $userSettings->server_url && $userSettings->username) {
                $url = rtrim($userSettings->server_url, '/') . '/status.php';
                $status = self::checkUrl($url, $userSettings->timeout ?: 30);
                
                if ($status['online']) {
                    // Try to authenticate to verify connection
                    $decryptedPassword = $userSettings->getDecryptedPassword();
                    if ($decryptedPassword) {
                        $authStatus = self::checkNextcloudAuth($userSettings->server_url, $userSettings->username, $decryptedPassword);
                        return [
                            'online' => true,
                            'authenticated' => $authStatus['authenticated'],
                            'server_url' => $userSettings->server_url,
                            'username' => $userSettings->username,
                            'message' => $authStatus['authenticated'] ? 'Връзката е успешна' : 'Сървърът е онлайн, но има проблем с автентикацията'
                        ];
                    }
                }
                
                return [
                    'online' => $status['online'],
                    'authenticated' => false,
                    'server_url' => $userSettings->server_url,
                    'username' => $userSettings->username,
                    'message' => $status['online'] ? 'Сървърът е онлайн, но няма конфигурирана парола' : 'Сървърът не отговаря'
                ];
            }
            
            // Fallback to global config
            $config = require __DIR__ . '/../../config/nextcloud.php';
            $globalUrl = $config['server']['url'];
            $globalUsername = $config['auth']['username'];
            $globalPassword = $config['auth']['password'];
            
            if ($globalUrl && $globalUsername && $globalPassword) {
                $url = rtrim($globalUrl, '/') . '/status.php';
                $status = self::checkUrl($url, $config['server']['timeout']);
                
                if ($status['online']) {
                    $authStatus = self::checkNextcloudAuth($globalUrl, $globalUsername, $globalPassword);
                    return [
                        'online' => true,
                        'authenticated' => $authStatus['authenticated'],
                        'server_url' => $globalUrl,
                        'username' => $globalUsername,
                        'message' => $authStatus['authenticated'] ? 'Връзката е успешна (глобални настройки)' : 'Сървърът е онлайн, но има проблем с автентикацията'
                    ];
                }
                
                return [
                    'online' => false,
                    'authenticated' => false,
                    'server_url' => $globalUrl,
                    'username' => $globalUsername,
                    'message' => 'Сървърът не отговаря'
                ];
            }
            
            return [
                'online' => false,
                'authenticated' => false,
                'server_url' => null,
                'username' => null,
                'message' => 'Nextcloud не е конфигуриран'
            ];
            
        } catch (\Exception $e) {
            return [
                'online' => false,
                'authenticated' => false,
                'server_url' => null,
                'username' => null,
                'message' => 'Грешка при проверка: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Check StrixBudget server status
     */
    public static function checkStrixBudgetStatus(int $userId): array
    {
        try {
            // Get user settings first
            $userSettings = UserStrixBudgetSettings::getByUserId($userId);
            
            if ($userSettings && $userSettings->isConfigured()) {
                $config = $userSettings->getClientConfig();
                $apiUrl = $config['api_url'];
                
                // Check if API is reachable
                $healthUrl = rtrim($apiUrl, '/') . '/health';
                $status = self::checkUrl($healthUrl, 30);
                
                if ($status['online']) {
                    // Try to authenticate
                    try {
                        $client = new StrixBudgetClient($apiUrl);
                        
                        if ($userSettings->usesCredentials()) {
                            $loginResult = $client->loginAndGetToken($config['api_email'], $config['api_password']);
                            $authenticated = $loginResult['success'];
                        } else {
                            $client->setToken($config['api_token']);
                            $testResult = $client->testConnection();
                            $authenticated = $testResult['success'];
                        }
                        
                        return [
                            'online' => true,
                            'authenticated' => $authenticated,
                            'api_url' => $apiUrl,
                            'auth_method' => $userSettings->getAuthMethod(),
                            'message' => $authenticated ? 'Връзката е успешна' : 'API е онлайн, но има проблем с автентикацията'
                        ];
                        
                    } catch (\Exception $e) {
                        return [
                            'online' => true,
                            'authenticated' => false,
                            'api_url' => $apiUrl,
                            'auth_method' => $userSettings->getAuthMethod(),
                            'message' => 'API е онлайн, но има грешка при автентикация: ' . $e->getMessage()
                        ];
                    }
                }
                
                return [
                    'online' => false,
                    'authenticated' => false,
                    'api_url' => $apiUrl,
                    'auth_method' => $userSettings->getAuthMethod(),
                    'message' => 'API сървърът не отговаря'
                ];
            }
            
            // Fallback to environment variables
            $apiUrl = $_ENV['STRIXBUDGET_API_URL'] ?? 'http://localhost:8000/api';
            $healthUrl = rtrim($apiUrl, '/') . '/health';
            $status = self::checkUrl($healthUrl, 30);
            
            return [
                'online' => $status['online'],
                'authenticated' => false,
                'api_url' => $apiUrl,
                'auth_method' => 'none',
                'message' => $status['online'] ? 'API е онлайн, но няма конфигурирани настройки' : 'StrixBudget не е конфигуриран или не отговаря'
            ];
            
        } catch (\Exception $e) {
            return [
                'online' => false,
                'authenticated' => false,
                'api_url' => null,
                'auth_method' => null,
                'message' => 'Грешка при проверка: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Check if URL is reachable
     */
    private static function checkUrl(string $url, int $timeout = 30): array
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_USERAGENT => 'Strix-ERP/1.0 Status-Checker'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($response === false || !empty($error)) {
            return ['online' => false, 'error' => $error];
        }
        
        return ['online' => $httpCode >= 200 && $httpCode < 400, 'http_code' => $httpCode];
    }
    
    /**
     * Check Nextcloud authentication
     */
    private static function checkNextcloudAuth(string $serverUrl, string $username, string $password): array
    {
        $webdavUrl = rtrim($serverUrl, '/') . '/remote.php/dav/files/' . $username . '/';
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $webdavUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_CUSTOMREQUEST => 'PROPFIND',
            CURLOPT_USERPWD => $username . ':' . $password,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_USERAGENT => 'Strix-ERP/1.0 Auth-Checker'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return ['authenticated' => $httpCode === 207]; // 207 Multi-Status is success for PROPFIND
    }
}
